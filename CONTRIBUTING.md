# Contributing to <PERSON><PERSON><PERSON> MCP

We welcome contributions to Fetcher MCP! By participating in this project, you agree to abide by our [Code of Conduct](CODE_OF_CONDUCT.md).

## How to Contribute

1.  **Fork the repository** on GitHub.
2.  **Clone your fork** to your local machine.
3.  **Set up your development environment** (see [Development Setup](#development-setup)).
4.  **Create a new branch** for your contribution.
5.  **Make your changes** and commit them with clear, concise commit messages.
6.  **Push your branch** to your fork on GitHub.
7.  **Submit a pull request** to the main repository.

## Development Setup

*   Install [Node.js](https://nodejs.org/) and npm.
*   Run `npm install` to install dependencies.
*   Run `npm run build` to build the project.

## Code Style Guide

*   Follow the [JavaScript Standard Style](https://standardjs.com/).
*   Write clear, concise, and well-commented code.
*   Test your code thoroughly.

## Submitting Issues

*   Use the issue templates provided.
*   Provide detailed description of the issue, including steps to reproduce it.
*   If possible, suggest a solution or workaround.

## Submitting Pull Requests

*   Follow the pull request template.
*   Ensure your code is well-tested and passes all checks.
*   Write clear and concise commit messages.
*   Explain the purpose of your pull request and the changes you've made.

## Code of Conduct

Please review and follow our [Code of Conduct](CODE_OF_CONDUCT.md).

Thank you for your contributions!