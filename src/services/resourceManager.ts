import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "playwright";
import { IResourceManager } from "../interfaces/enhanced-fetcher.js";
import { IBrowserService } from "../interfaces/fetcher.js";
import { FetchOptions } from "../types/index.js";
import { logger } from "../utils/logger.js";

/**
 * 资源管理器实现
 */
export class ResourceManager implements IResourceManager {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private pages: Set<Page> = new Set();
  private browserService: IBrowserService;
  private options: FetchOptions;
  private viewport: { width: number; height: number } | null = null;

  constructor(browserService: IBrowserService, options: FetchOptions) {
    this.browserService = browserService;
    this.options = options;
  }

  /**
   * 获取浏览器实例
   */
  async getBrowser(): Promise<Browser> {
    if (!this.browser) {
      logger.debug('[ResourceManager] Creating new browser instance');
      this.browser = await this.browserService.createBrowser();
    }
    if (!this.browser) {
      throw new Error('Failed to create browser instance');
    }
    return this.browser;
  }

  /**
   * 获取浏览器上下文
   */
  private async getContext(): Promise<{ context: BrowserContext; viewport: { width: number; height: number } }> {
    if (!this.context || !this.viewport) {
      const browser = await this.getBrowser();
      logger.debug('[ResourceManager] Creating new browser context');
      const result = await this.browserService.createContext(browser);
      this.context = result.context;
      this.viewport = result.viewport;
    }
    if (!this.context || !this.viewport) {
      throw new Error('Failed to create browser context or viewport');
    }
    return { context: this.context, viewport: this.viewport };
  }

  /**
   * 获取页面实例
   */
  async getPage(url: string): Promise<Page> {
    const { context, viewport } = await this.getContext();
    
    logger.debug(`[ResourceManager] Creating new page for URL: ${url}`);
    const page = await this.browserService.createPage(context, viewport);
    
    this.pages.add(page);
    
    // 设置页面关闭监听器
    page.on('close', () => {
      this.pages.delete(page);
      logger.debug(`[ResourceManager] Page closed and removed from tracking`);
    });

    return page;
  }

  /**
   * 释放页面实例
   */
  async releasePage(page: Page): Promise<void> {
    if (!this.pages.has(page)) {
      logger.warn('[ResourceManager] Attempting to release page that is not tracked');
      return;
    }

    try {
      if (!this.browserService.isInDebugMode()) {
        await page.close();
        logger.debug('[ResourceManager] Page closed and released');
      } else {
        logger.debug('[ResourceManager] Page kept open for debugging');
      }
    } catch (error: any) {
      logger.error(`[ResourceManager] Failed to close page: ${error.message}`);
    } finally {
      this.pages.delete(page);
    }
  }

  /**
   * 清理所有资源
   */
  async cleanup(): Promise<void> {
    logger.debug('[ResourceManager] Starting cleanup process');

    // 清理所有页面
    const pageCleanupPromises = Array.from(this.pages).map(async (page) => {
      try {
        if (!this.browserService.isInDebugMode()) {
          await page.close();
        }
      } catch (error: any) {
        logger.error(`[ResourceManager] Failed to close page during cleanup: ${error.message}`);
      }
    });

    await Promise.all(pageCleanupPromises);
    this.pages.clear();

    // 清理浏览器上下文
    if (this.context && !this.browserService.isInDebugMode()) {
      try {
        await this.context.close();
        logger.debug('[ResourceManager] Browser context closed');
      } catch (error: any) {
        logger.error(`[ResourceManager] Failed to close browser context: ${error.message}`);
      }
      this.context = null;
      this.viewport = null;
    }

    // 清理浏览器
    if (this.browser && !this.browserService.isInDebugMode()) {
      try {
        await this.browser.close();
        logger.debug('[ResourceManager] Browser closed');
      } catch (error: any) {
        logger.error(`[ResourceManager] Failed to close browser: ${error.message}`);
      }
      this.browser = null;
    }

    if (this.browserService.isInDebugMode()) {
      logger.debug('[ResourceManager] Resources kept open for debugging');
    } else {
      logger.debug('[ResourceManager] Cleanup completed');
    }
  }

  /**
   * 获取资源状态
   */
  getResourceStatus(): {
    hasBrowser: boolean;
    hasContext: boolean;
    pageCount: number;
    isDebugMode: boolean;
  } {
    return {
      hasBrowser: this.browser !== null,
      hasContext: this.context !== null,
      pageCount: this.pages.size,
      isDebugMode: this.browserService.isInDebugMode(),
    };
  }

  /**
   * 强制清理（忽略调试模式）
   */
  async forceCleanup(): Promise<void> {
    logger.debug('[ResourceManager] Starting force cleanup process');

    // 强制清理所有页面
    const pageCleanupPromises = Array.from(this.pages).map(async (page) => {
      try {
        await page.close();
      } catch (error: any) {
        logger.error(`[ResourceManager] Failed to force close page: ${error.message}`);
      }
    });

    await Promise.all(pageCleanupPromises);
    this.pages.clear();

    // 强制清理浏览器上下文
    if (this.context) {
      try {
        await this.context.close();
        logger.debug('[ResourceManager] Browser context force closed');
      } catch (error: any) {
        logger.error(`[ResourceManager] Failed to force close browser context: ${error.message}`);
      }
      this.context = null;
      this.viewport = null;
    }

    // 强制清理浏览器
    if (this.browser) {
      try {
        await this.browser.close();
        logger.debug('[ResourceManager] Browser force closed');
      } catch (error: any) {
        logger.error(`[ResourceManager] Failed to force close browser: ${error.message}`);
      }
      this.browser = null;
    }

    logger.debug('[ResourceManager] Force cleanup completed');
  }
}
