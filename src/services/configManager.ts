import { FetchOptions } from "../types/index.js";
import { IConfigManager } from "../interfaces/enhanced-fetcher.js";

/**
 * 配置管理器实现
 */
export class ConfigManager implements IConfigManager {
  private config: Map<string, any> = new Map();
  private defaultOptions: FetchOptions;

  constructor() {
    this.defaultOptions = this.createDefaultOptions();
    this.reset();
  }

  /**
   * 创建默认配置
   */
  private createDefaultOptions(): FetchOptions {
    return {
      timeout: 30000,
      waitUntil: 'load',
      extractContent: true,
      maxLength: 0,
      returnHtml: false,
      waitForNavigation: false,
      navigationTimeout: 10000,
      disableMedia: true,
      debug: false,
    };
  }

  /**
   * 获取配置
   */
  get<T>(key: string): T | undefined {
    return this.config.get(key) as T;
  }

  /**
   * 设置配置
   */
  set<T>(key: string, value: T): void {
    this.config.set(key, value);
  }

  /**
   * 合并配置
   */
  merge(options: Partial<FetchOptions>): FetchOptions {
    const baseOptions = this.get<FetchOptions>('fetchOptions') || this.defaultOptions;
    
    return {
      timeout: options.timeout ?? baseOptions.timeout,
      waitUntil: options.waitUntil ?? baseOptions.waitUntil,
      extractContent: options.extractContent ?? baseOptions.extractContent,
      maxLength: options.maxLength ?? baseOptions.maxLength,
      returnHtml: options.returnHtml ?? baseOptions.returnHtml,
      waitForNavigation: options.waitForNavigation ?? baseOptions.waitForNavigation,
      navigationTimeout: options.navigationTimeout ?? baseOptions.navigationTimeout,
      disableMedia: options.disableMedia ?? baseOptions.disableMedia,
      debug: options.debug ?? baseOptions.debug,
    };
  }

  /**
   * 重置为默认配置
   */
  reset(): void {
    this.config.clear();
    this.config.set('fetchOptions', { ...this.defaultOptions });
  }

  /**
   * 获取默认配置
   */
  getDefaultOptions(): FetchOptions {
    return { ...this.defaultOptions };
  }

  /**
   * 设置默认配置
   */
  setDefaultOptions(options: Partial<FetchOptions>): void {
    this.defaultOptions = this.merge(options);
    this.config.set('fetchOptions', { ...this.defaultOptions });
  }

  /**
   * 验证配置
   */
  validateOptions(options: Partial<FetchOptions>): string[] {
    const errors: string[] = [];

    if (options.timeout !== undefined && (options.timeout <= 0 || options.timeout > 300000)) {
      errors.push('Timeout must be between 1 and 300000 milliseconds');
    }

    if (options.navigationTimeout !== undefined && (options.navigationTimeout <= 0 || options.navigationTimeout > 60000)) {
      errors.push('Navigation timeout must be between 1 and 60000 milliseconds');
    }

    if (options.maxLength !== undefined && options.maxLength < 0) {
      errors.push('Max length must be non-negative');
    }

    if (options.waitUntil !== undefined) {
      const validWaitUntil = ['load', 'domcontentloaded', 'networkidle', 'commit'];
      if (!validWaitUntil.includes(options.waitUntil)) {
        errors.push(`WaitUntil must be one of: ${validWaitUntil.join(', ')}`);
      }
    }

    return errors;
  }
}
