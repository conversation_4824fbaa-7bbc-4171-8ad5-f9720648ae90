import { IBrowserService, IContentProcessor } from "../interfaces/fetcher.js";
import { BrowserService } from "./browserService.js";
import { WebContentProcessor } from "./webContentProcessor.js";
import { FetchOptions } from "../types/index.js";

/**
 * 服务工厂类 - 负责创建和管理所有服务实例
 */
export class FetcherFactory {
  /**
   * 创建浏览器服务实例
   * @param options 抓取选项
   */
  static createBrowserService(options: FetchOptions): IBrowserService {
    return new BrowserService(options);
  }

  /**
   * 创建内容处理器实例
   * @param options 抓取选项
   * @param logPrefix 日志前缀
   */
  static createContentProcessor(options: FetchOptions, logPrefix: string = ""): IContentProcessor {
    return new WebContentProcessor(options, logPrefix);
  }
}