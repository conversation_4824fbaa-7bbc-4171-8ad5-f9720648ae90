import { IContentFetcher, IBrowserService, IContentProcessor } from "../interfaces/fetcher.js";
import {
  IEnhancedContentFetcher,
  IConfigManager,
  IErrorHandler,
  IResourceManager
} from "../interfaces/enhanced-fetcher.js";
import { BrowserService } from "./browserService.js";
import { WebContentProcessor } from "./webContentProcessor.js";
import { ConfigManager } from "./configManager.js";
import { ErrorHandler } from "./errorHandler.js";
import { ResourceManager } from "./resourceManager.js";
import { EnhancedContentFetcher } from "./enhancedContentFetcher.js";
import { FetchOptions } from "../types/index.js";

/**
 * 抓取器工厂类
 */
export class FetcherFactory {
  /**
   * 创建浏览器服务实例
   * @param options 抓取选项
   */
  static createBrowserService(options: FetchOptions): IBrowserService {
    return new BrowserService(options);
  }

  /**
   * 创建内容处理器实例
   * @param options 抓取选项
   * @param logPrefix 日志前缀
   */
  static createContentProcessor(options: FetchOptions, logPrefix: string = ""): IContentProcessor {
    return new WebContentProcessor(options, logPrefix);
  }

  /**
   * 创建配置管理器实例
   */
  static createConfigManager(): IConfigManager {
    return new ConfigManager();
  }

  /**
   * 创建错误处理器实例
   */
  static createErrorHandler(): IErrorHandler {
    return new ErrorHandler();
  }

  /**
   * 创建资源管理器实例
   * @param browserService 浏览器服务
   * @param options 抓取选项
   */
  static createResourceManager(browserService: IBrowserService, options: FetchOptions): IResourceManager {
    return new ResourceManager(browserService, options);
  }

  /**
   * 创建增强的内容抓取器实例
   * @param options 抓取选项
   * @param logPrefix 日志前缀
   */
  static createEnhancedContentFetcher(options: FetchOptions, logPrefix: string = ""): IEnhancedContentFetcher {
    const configManager = this.createConfigManager();
    const errorHandler = this.createErrorHandler();
    const browserService = this.createBrowserService(options);
    const resourceManager = this.createResourceManager(browserService, options);
    const contentProcessor = this.createContentProcessor(options, logPrefix);

    // 设置默认配置
    configManager.setDefaultOptions(options);

    return new EnhancedContentFetcher(
      configManager,
      errorHandler,
      resourceManager,
      contentProcessor
    );
  }
}