import { <PERSON><PERSON><PERSON> } from "jsdom";
import { Readability } from "@mozilla/readability";
import TurndownService from "turndown";
import { Page } from "playwright";
import { FetchOptions, FetchResult } from "../types/index.js";
import { logger } from "../utils/logger.js";
import { IContentProcessor } from "../interfaces/fetcher.js";

/**
 * 网页内容处理器 - 负责页面导航、内容提取和格式转换
 */
export class WebContentProcessor implements IContentProcessor {
  private readonly options: FetchOptions;
  private readonly logPrefix: string;

  constructor(options: FetchOptions, logPrefix: string = "") {
    this.options = options;
    this.logPrefix = logPrefix;
  }

  /**
   * 处理页面内容 - 导航到URL并提取内容
   */
  async processPageContent(page: Page, url: string): Promise<FetchResult> {
    try {
      logger.info(`${this.logPrefix} Navigating to URL: ${url}`);
      
      // 设置页面超时
      page.setDefaultTimeout(this.options.timeout);

      // 导航到目标URL
      await page.goto(url, {
        timeout: this.options.timeout,
        waitUntil: this.options.waitUntil,
      });

      // 如果需要等待额外导航（反爬虫验证）
      if (this.options.waitForNavigation) {
        try {
          await page.waitForLoadState('networkidle', { timeout: this.options.navigationTimeout });
          logger.info(`${this.logPrefix} Additional navigation completed`);
        } catch (waitError: any) {
          logger.warn(`${this.logPrefix} Additional navigation wait failed: ${waitError.message}`);
        }
      }

      // 获取页面标题和内容
      const [pageTitle, html] = await Promise.all([
        page.title().catch(() => "Untitled"),
        page.content().catch(() => "")
      ]);

      if (!html || html.trim().length === 0) {
        throw new Error("Browser returned empty content");
      }

      logger.info(`${this.logPrefix} Retrieved content, length: ${html.length}`);

      // 处理内容
      const processedContent = await this.processContent(html, url);
      const formattedContent = `Title: ${pageTitle}\nURL: ${url}\nContent:\n\n${processedContent}`;

      return {
        success: true,
        content: [{ type: "text", text: formattedContent }],
      };
    } catch (error: any) {
      const errorMessage = error?.message || "Unknown error";
      logger.error(`${this.logPrefix} Error: ${errorMessage}`);

      return {
        success: false,
        content: [{ type: "text", text: `Title: Error\nURL: ${url}\nContent:\n\n<error>Failed to retrieve web page content: ${errorMessage}</error>` }],
        error: errorMessage,
      };
    }
  }

  /**
   * 处理HTML内容 - 提取主要内容并转换格式
   */
  private async processContent(html: string, url: string): Promise<string> {
    let contentToProcess = html;

    // 智能提取主要内容
    if (this.options.extractContent) {
      try {
        const dom = new JSDOM(html, { url });
        const reader = new Readability(dom.window.document);
        const article = reader.parse();

        if (article?.content) {
          contentToProcess = article.content;
          logger.info(`${this.logPrefix} Successfully extracted main content, length: ${contentToProcess.length}`);
        } else {
          logger.warn(`${this.logPrefix} Could not extract main content, using full HTML`);
        }
      } catch (error: any) {
        logger.warn(`${this.logPrefix} Content extraction failed: ${error.message}, using full HTML`);
      }
    }

    // 转换为Markdown格式（如果需要）
    if (!this.options.returnHtml) {
      try {
        const turndownService = new TurndownService({
          headingStyle: 'atx',
          codeBlockStyle: 'fenced',
          bulletListMarker: '-',
          emDelimiter: '*',
          strongDelimiter: '**',
        });

        // 配置转换规则
        turndownService.addRule('removeScript', {
          filter: ['script', 'style', 'noscript'],
          replacement: () => ''
        });

        turndownService.addRule('preserveLinks', {
          filter: 'a',
          replacement: (content, node: any) => {
            const href = node.getAttribute('href');
            if (!href || href.startsWith('#')) return content;
            return `[${content}](${href})`;
          }
        });

        const markdown = turndownService.turndown(contentToProcess);
        logger.info(`${this.logPrefix} Converted to Markdown, length: ${markdown.length}`);
        return markdown;
      } catch (error: any) {
        logger.warn(`${this.logPrefix} Markdown conversion failed: ${error.message}, returning HTML`);
      }
    }

    return contentToProcess;
  }
}
