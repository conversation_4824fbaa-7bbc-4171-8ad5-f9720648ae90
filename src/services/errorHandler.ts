import { FetchResult } from "../types/index.js";
import { I<PERSON>rror<PERSON><PERSON><PERSON>, <PERSON>rror<PERSON>ontext, RetryConfig } from "../interfaces/enhanced-fetcher.js";
import { logger } from "../utils/logger.js";

/**
 * 错误处理器实现
 */
export class ErrorHandler implements IErrorHandler {
  private retryConfig: RetryConfig;

  constructor(retryConfig?: Partial<RetryConfig>) {
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2,
      retryableErrors: [
        'TimeoutError',
        'NetworkError',
        'ConnectionError',
        'ECONNRESET',
        'ENOTFOUND',
        'ECONNREFUSED',
        'ERR_NETWORK_CHANGED',
        'ERR_INTERNET_DISCONNECTED'
      ],
      ...retryConfig
    };
  }

  /**
   * 处理错误
   */
  async handle(error: Error, context: ErrorContext): Promise<FetchResult> {
    const errorMessage = error.message || String(error);
    const logPrefix = `[${context.operation}]`;
    
    logger.error(`${logPrefix} Error processing ${context.url}: ${errorMessage}`);

    // 生成用户友好的错误信息
    const userFriendlyMessage = this.generateUserFriendlyMessage(error, context);
    
    // 检查是否是 Playwright 相关错误
    const hint = this.generateHint(error);

    return {
      success: false,
      content: [{
        type: "text",
        text: `Title: Error\nURL: ${context.url}\nContent:\n\n<error>${userFriendlyMessage}</error>${hint}`
      }],
      error: errorMessage,
    };
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error: Error, retryCount: number): boolean {
    if (retryCount >= this.retryConfig.maxRetries) {
      return false;
    }

    const errorMessage = error.message || String(error);
    
    return this.retryConfig.retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError)
    );
  }

  /**
   * 计算重试延迟
   */
  calculateRetryDelay(retryCount: number): number {
    return this.retryConfig.retryDelay * Math.pow(this.retryConfig.backoffMultiplier, retryCount);
  }

  /**
   * 生成用户友好的错误信息
   */
  private generateUserFriendlyMessage(error: Error, context: ErrorContext): string {
    const errorMessage = error.message || String(error);

    if (errorMessage.includes('TimeoutError') || errorMessage.includes('timeout')) {
      return `页面加载超时。建议增加超时时间或检查网络连接。当前超时设置: ${context.options.timeout}ms`;
    }

    if (errorMessage.includes('net::ERR_NAME_NOT_RESOLVED')) {
      return '无法解析域名，请检查URL是否正确或网络连接是否正常';
    }

    if (errorMessage.includes('net::ERR_CONNECTION_REFUSED')) {
      return '连接被拒绝，目标服务器可能不可用';
    }

    if (errorMessage.includes('net::ERR_CERT_')) {
      return 'SSL证书错误，网站的安全证书可能有问题';
    }

    if (errorMessage.includes('net::ERR_TOO_MANY_REDIRECTS')) {
      return '重定向次数过多，可能存在重定向循环';
    }

    if (errorMessage.includes('403')) {
      return '访问被禁止，可能需要登录或网站阻止了自动化访问';
    }

    if (errorMessage.includes('404')) {
      return '页面不存在，请检查URL是否正确';
    }

    if (errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503')) {
      return '服务器错误，请稍后重试';
    }

    return `抓取失败: ${errorMessage}`;
  }

  /**
   * 生成提示信息
   */
  private generateHint(error: Error): string {
    const errorMessage = error.message || String(error);
    
    if (errorMessage.includes('playwright') || errorMessage.includes('chromium')) {
      return ' 提示: 请确保已安装 Chromium 浏览器，运行: npx playwright install chromium';
    }

    if (errorMessage.includes('TimeoutError')) {
      return ' 提示: 可以尝试增加超时时间或启用 waitForNavigation 选项';
    }

    if (errorMessage.includes('403') || errorMessage.includes('blocked')) {
      return ' 提示: 可以尝试启用调试模式手动登录，或调整请求头信息';
    }

    return '';
  }

  /**
   * 获取重试配置
   */
  getRetryConfig(): RetryConfig {
    return { ...this.retryConfig };
  }

  /**
   * 更新重试配置
   */
  updateRetryConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config };
  }

  /**
   * 等待指定时间
   */
  async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
