import { FetchOptions, FetchResult } from "../types/index.js";
import { 
  IEnhancedContentFetcher, 
  IConfigManager, 
  IErrorHandler, 
  IResourceManager,
  ErrorContext 
} from "../interfaces/enhanced-fetcher.js";
import { IContentProcessor } from "../interfaces/fetcher.js";
import { logger } from "../utils/logger.js";

/**
 * 增强的内容抓取器实现
 */
export class EnhancedContentFetcher implements IEnhancedContentFetcher {
  private configManager: IConfigManager;
  private errorHandler: IErrorHandler;
  private resourceManager: IResourceManager;
  private contentProcessor: IContentProcessor;

  constructor(
    configManager: IConfigManager,
    errorHandler: IErrorHandler,
    resourceManager: IResourceManager,
    contentProcessor: IContentProcessor
  ) {
    this.configManager = configManager;
    this.errorHandler = errorHandler;
    this.resourceManager = resourceManager;
    this.contentProcessor = contentProcessor;
  }

  /**
   * 抓取单个URL的内容
   */
  async fetchUrl(url: string, options?: Partial<FetchOptions>): Promise<FetchResult> {
    // 验证URL
    if (!this.validateUrl(url)) {
      return {
        success: false,
        content: [{ type: "text", text: "Invalid URL format" }],
        error: "Invalid URL format"
      };
    }

    // 合并配置
    const mergedOptions = this.configManager.merge(options || {});
    
    // 验证配置
    const configErrors = this.configManager.validateOptions(mergedOptions);
    if (configErrors.length > 0) {
      return {
        success: false,
        content: [{ type: "text", text: `Configuration errors: ${configErrors.join(', ')}` }],
        error: `Configuration errors: ${configErrors.join(', ')}`
      };
    }

    const context: ErrorContext = {
      url,
      options: mergedOptions,
      retryCount: 0,
      operation: 'fetchUrl'
    };

    return this.executeWithRetry(url, mergedOptions, context);
  }

  /**
   * 批量抓取多个URL的内容
   */
  async fetchUrls(urls: string[], options?: Partial<FetchOptions>): Promise<FetchResult> {
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return {
        success: false,
        content: [{ type: "text", text: "URLs parameter is required and must be a non-empty array" }],
        error: "URLs parameter is required and must be a non-empty array"
      };
    }

    // 验证所有URL
    const invalidUrls = urls.filter(url => !this.validateUrl(url));
    if (invalidUrls.length > 0) {
      return {
        success: false,
        content: [{ type: "text", text: `Invalid URLs: ${invalidUrls.join(', ')}` }],
        error: `Invalid URLs: ${invalidUrls.join(', ')}`
      };
    }

    // 合并配置
    const mergedOptions = this.configManager.merge(options || {});
    
    // 验证配置
    const configErrors = this.configManager.validateOptions(mergedOptions);
    if (configErrors.length > 0) {
      return {
        success: false,
        content: [{ type: "text", text: `Configuration errors: ${configErrors.join(', ')}` }],
        error: `Configuration errors: ${configErrors.join(', ')}`
      };
    }

    logger.info(`[EnhancedFetcher] Starting batch fetch for ${urls.length} URLs`);

    try {
      const results = await Promise.all(
        urls.map(async (url, index) => {
          const context: ErrorContext = {
            url,
            options: mergedOptions,
            retryCount: 0,
            operation: 'fetchUrls'
          };

          try {
            const result = await this.executeWithRetry(url, mergedOptions, context);
            return { index, ...result };
          } catch (error: any) {
            logger.error(`[EnhancedFetcher] Failed to fetch ${url}: ${error.message}`);
            const errorResult = await this.errorHandler.handle(error, context);
            return { index, ...errorResult };
          }
        })
      );

      // 排序并合并结果
      results.sort((a, b) => (a.index || 0) - (b.index || 0));
      const combinedResults = results
        .map((result, i) => `[webpage ${i + 1} begin]\n${result.content[0]?.text || ''}\n[webpage ${i + 1} end]`)
        .join("\n\n");

      return {
        success: true,
        content: [{ type: "text", text: combinedResults }]
      };
    } catch (error: any) {
      logger.error(`[EnhancedFetcher] Top-level batch fetch error: ${error.message}`);
      const context: ErrorContext = {
        url: urls.join(', '),
        options: mergedOptions,
        retryCount: 0,
        operation: 'fetchUrls'
      };
      return this.errorHandler.handle(error, context);
    }
  }

  /**
   * 带重试机制的执行
   */
  private async executeWithRetry(url: string, options: FetchOptions, context: ErrorContext): Promise<FetchResult> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.errorHandler.getRetryConfig().maxRetries; attempt++) {
      context.retryCount = attempt;

      try {
        if (attempt > 0) {
          const delay = this.errorHandler.calculateRetryDelay(attempt - 1);
          logger.info(`[EnhancedFetcher] Retrying ${url} (attempt ${attempt + 1}) after ${delay}ms delay`);
          await this.sleep(delay);
        }

        return await this.executeSingleFetch(url, options);
      } catch (error: any) {
        lastError = error;
        
        if (!this.errorHandler.shouldRetry(error, attempt)) {
          logger.error(`[EnhancedFetcher] Not retrying ${url} due to error type or max retries reached`);
          break;
        }
        
        logger.warn(`[EnhancedFetcher] Attempt ${attempt + 1} failed for ${url}: ${error.message}`);
      }
    }

    // 所有重试都失败了
    return this.errorHandler.handle(lastError!, context);
  }

  /**
   * 执行单次抓取
   */
  private async executeSingleFetch(url: string, options: FetchOptions): Promise<FetchResult> {
    const page = await this.resourceManager.getPage(url);
    
    try {
      const result = await this.contentProcessor.processPageContent(page, url);

      return {
        success: true,
        content: result.content
      };
    } finally {
      await this.resourceManager.releasePage(page);
    }
  }

  /**
   * 获取默认配置
   */
  getDefaultOptions(): FetchOptions {
    return this.configManager.getDefaultOptions();
  }

  /**
   * 设置默认配置
   */
  setDefaultOptions(options: Partial<FetchOptions>): void {
    this.configManager.setDefaultOptions(options);
  }

  /**
   * 验证URL格式
   */
  validateUrl(url: string): boolean {
    if (!url || typeof url !== 'string') {
      return false;
    }

    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    await this.resourceManager.cleanup();
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
