import { FetchOptions, FetchResult } from "../types/index.js";

/**
 * 增强的网页内容抓取器接口
 */
export interface IEnhancedContentFetcher {
  /**
   * 抓取单个URL的内容
   * @param url 目标URL
   * @param options 抓取选项
   */
  fetchUrl(url: string, options?: Partial<FetchOptions>): Promise<FetchResult>;

  /**
   * 批量抓取多个URL的内容
   * @param urls 目标URL数组
   * @param options 抓取选项
   */
  fetchUrls(urls: string[], options?: Partial<FetchOptions>): Promise<FetchResult>;

  /**
   * 获取默认配置
   */
  getDefaultOptions(): FetchOptions;

  /**
   * 设置默认配置
   * @param options 配置选项
   */
  setDefaultOptions(options: Partial<FetchOptions>): void;

  /**
   * 验证URL格式
   * @param url 待验证的URL
   */
  validateUrl(url: string): boolean;

  /**
   * 清理资源
   */
  cleanup(): Promise<void>;
}

/**
 * 抓取策略接口
 */
export interface IFetchStrategy {
  /**
   * 策略名称
   */
  readonly name: string;

  /**
   * 执行抓取
   * @param url 目标URL
   * @param options 抓取选项
   */
  execute(url: string, options: FetchOptions): Promise<FetchResult>;

  /**
   * 是否支持该URL
   * @param url 目标URL
   */
  supports(url: string): boolean;
}

/**
 * 配置管理器接口
 */
export interface IConfigManager {
  /**
   * 获取配置
   * @param key 配置键
   */
  get<T>(key: string): T | undefined;

  /**
   * 设置配置
   * @param key 配置键
   * @param value 配置值
   */
  set<T>(key: string, value: T): void;

  /**
   * 合并配置
   * @param options 配置选项
   */
  merge(options: Partial<FetchOptions>): FetchOptions;

  /**
   * 重置为默认配置
   */
  reset(): void;

  /**
   * 获取默认配置
   */
  getDefaultOptions(): FetchOptions;

  /**
   * 设置默认配置
   * @param options 配置选项
   */
  setDefaultOptions(options: Partial<FetchOptions>): void;

  /**
   * 验证配置
   * @param options 配置选项
   */
  validateOptions(options: Partial<FetchOptions>): string[];
}

/**
 * 错误处理器接口
 */
export interface IErrorHandler {
  /**
   * 处理错误
   * @param error 错误对象
   * @param context 上下文信息
   */
  handle(error: Error, context: ErrorContext): Promise<FetchResult>;

  /**
   * 是否应该重试
   * @param error 错误对象
   * @param retryCount 当前重试次数
   */
  shouldRetry(error: Error, retryCount: number): boolean;

  /**
   * 获取重试配置
   */
  getRetryConfig(): RetryConfig;

  /**
   * 计算重试延迟
   * @param retryCount 重试次数
   */
  calculateRetryDelay(retryCount: number): number;
}

/**
 * 错误上下文
 */
export interface ErrorContext {
  url: string;
  options: FetchOptions;
  retryCount: number;
  operation: 'fetchUrl' | 'fetchUrls';
}

/**
 * 重试配置
 */
export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  retryableErrors: string[];
}

/**
 * 资源管理器接口
 */
export interface IResourceManager {
  /**
   * 获取浏览器实例
   */
  getBrowser(): Promise<any>;

  /**
   * 获取页面实例
   * @param url 目标URL
   */
  getPage(url: string): Promise<any>;

  /**
   * 释放页面实例
   * @param page 页面实例
   */
  releasePage(page: any): Promise<void>;

  /**
   * 清理所有资源
   */
  cleanup(): Promise<void>;
}
