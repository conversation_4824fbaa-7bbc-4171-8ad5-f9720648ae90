import { FetchOptions, FetchResult } from "../types/index.js";

/**
 * 网页内容抓取器接口
 */
export interface IContentFetcher {
  /**
   * 抓取单个URL的内容
   * @param url 目标URL
   * @param options 抓取选项
   */
  fetchUrl(url: string, options: FetchOptions): Promise<FetchResult>;

  /**
   * 批量抓取多个URL的内容
   * @param urls 目标URL数组
   * @param options 抓取选项
   */
  fetchUrls(urls: string[], options: FetchOptions): Promise<FetchResult>;
}

/**
 * 浏览器服务接口
 */
export interface IBrowserService {
  /**
   * 创建浏览器实例
   */
  createBrowser(): Promise<any>;

  /**
   * 创建浏览器上下文
   * @param browser 浏览器实例
   */
  createContext(browser: any): Promise<{ context: any; viewport: any }>;

  /**
   * 创建页面
   * @param context 浏览器上下文
   * @param viewport 视口设置
   */
  createPage(context: any, viewport: any): Promise<any>;

  /**
   * 清理资源
   * @param browser 浏览器实例
   * @param page 页面实例
   */
  cleanup(browser: any | null, page: any | null): Promise<void>;

  /**
   * 是否处于调试模式
   */
  isInDebugMode(): boolean;
}

/**
 * 网页内容处理器接口
 */
export interface IContentProcessor {
  /**
   * 处理页面内容
   * @param page 页面实例
   * @param url 目标URL
   */
  processPageContent(page: any, url: string): Promise<FetchResult>;
}