import { Browser, Browser<PERSON>ontext, Page } from "playwright";
import { FetchOptions, FetchResult } from "../types/index.js";

/**
 * 浏览器服务接口 - 负责浏览器生命周期管理
 */
export interface IBrowserService {
  /**
   * 创建浏览器实例
   */
  createBrowser(): Promise<Browser>;

  /**
   * 创建浏览器上下文
   * @param browser 浏览器实例
   */
  createContext(browser: Browser): Promise<{ context: BrowserContext; viewport: { width: number; height: number } }>;

  /**
   * 创建页面
   * @param context 浏览器上下文
   * @param viewport 视口设置
   */
  createPage(context: BrowserContext, viewport: { width: number; height: number }): Promise<Page>;

  /**
   * 清理资源
   * @param browser 浏览器实例
   * @param page 页面实例
   */
  cleanup(browser: Browser | null, page: Page | null): Promise<void>;

  /**
   * 是否处于调试模式
   */
  isInDebugMode(): boolean;
}

/**
 * 网页内容处理器接口 - 负责页面内容提取和处理
 */
export interface IContentProcessor {
  /**
   * 处理页面内容
   * @param page 页面实例
   * @param url 目标URL
   */
  processPageContent(page: Page, url: string): Promise<FetchResult>;
}