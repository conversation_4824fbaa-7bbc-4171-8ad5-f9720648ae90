export interface FetchOptions {
    timeout: number;
    waitUntil: 'load' | 'domcontentloaded' | 'networkidle' | 'commit';
    extractContent: boolean;
    maxLength: number;
    returnHtml: boolean;
    waitForNavigation: boolean;
    navigationTimeout: number;
    disableMedia: boolean;
    debug?: boolean;
  }
  
  export interface FetchResult {
    success?: boolean;
    content: Array<{ type: string; text: string }>;
    error?: string;
    index?: number;
    isError?: boolean; // 保持向后兼容性
  }