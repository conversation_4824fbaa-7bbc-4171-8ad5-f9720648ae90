/**
 * 网页抓取配置选项
 */
export interface FetchOptions {
  /** 页面加载超时时间（毫秒） */
  timeout: number;
  /** 页面加载完成的判断条件 */
  waitUntil: 'load' | 'domcontentloaded' | 'networkidle' | 'commit';
  /** 是否智能提取主要内容 */
  extractContent: boolean;
  /** 返回内容的最大长度（0表示无限制） */
  maxLength: number;
  /** 是否返回HTML格式而非Markdown */
  returnHtml: boolean;
  /** 是否等待额外的导航（用于反爬虫验证） */
  waitForNavigation: boolean;
  /** 额外导航的超时时间（毫秒） */
  navigationTimeout: number;
  /** 是否禁用媒体资源加载 */
  disableMedia: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 网页抓取结果
 */
export interface FetchResult {
  /** 操作是否成功 */
  success?: boolean;
  /** 抓取到的内容 */
  content: Array<{ type: string; text: string }>;
  /** 错误信息 */
  error?: string;
  /** 批量操作中的索引 */
  index?: number;
  /** 是否为错误结果（向后兼容） */
  isError?: boolean;
}