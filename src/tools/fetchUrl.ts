import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";
import { FetcherFactory } from "../services/fetcherFactory.js";
import { FetchOptions, FetchResult } from "../types/index.js";
import { logger } from "../utils/logger.js";
import { IContentFetcher } from "../interfaces/fetcher.js";

/**
 * Tool definition for fetch_url
 */
export const fetchUrlTool = {
  name: "fetch_url",
  // description: "Retrieve webpage content from a specified URL",
  description: "从指定网址精准提炼网页骨架，全面解析页面结构，输出条理清晰的标题、段落、链接、图片、表格及元数据，适用于内容洞察、网页抓取与高效生成结构化网页快照，支持 HTML 解析并以层次分明的清爽格式呈现。",
  inputSchema: {
    type: "object",
    properties: {
      url: {
        type: "string",
        description: "URL to fetch. Make sure to include the schema (http:// or https:// if not defined, preferring https for most cases)",
      },
      timeout: {
        type: "number",
        description:
          "Page loading timeout in milliseconds, default is 30000 (30 seconds)",
      },
      waitUntil: {
        type: "string",
        description:
          "Specifies when navigation is considered complete, options: 'load', 'domcontentloaded', 'networkidle', 'commit', default is 'load'",
      },
      extractContent: {
        type: "boolean",
        description:
          "Whether to intelligently extract the main content, default is true",
      },
      maxLength: {
        type: "number",
        description:
          "Maximum length of returned content (in characters), default is no limit",
      },
      returnHtml: {
        type: "boolean",
        description:
          "Whether to return HTML content instead of Markdown, default is false",
      },
      waitForNavigation: {
        type: "boolean",
        description:
          "Whether to wait for additional navigation after initial page load (useful for sites with anti-bot verification), default is false",
      },
      navigationTimeout: {
        type: "number",
        description:
          "Maximum time to wait for additional navigation in milliseconds, default is 10000 (10 seconds)",
      },
      disableMedia: {
        type: "boolean",
        description:
          "Whether to disable media resources (images, stylesheets, fonts, media), default is true",
      },
      debug: {
        type: "boolean",
        description:
          "Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified",
      },
    },
    required: ["url"],
  },
};

/**
 * Implementation of the fetch_url tool
 */
export async function fetchUrl(args: any): Promise<FetchResult> {
  const url = String(args?.url || "");
  if (!url) {
    logger.error(`URL parameter missing`);
    return {
      success: false,
      isError: true,
      content: [{ type: "text", text: "URL parameter is required" }],
      error: "URL parameter is required"
    };
  }
  
  // 验证URL格式
  try {
    const urlObj = new URL(url);
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      logger.error(`Invalid URL protocol: ${urlObj.protocol}`);
      return {
        success: false,
        isError: true,
        content: [{ type: "text", text: "URL must use http or https protocol" }],
        error: "URL must use http or https protocol"
      };
    }
  } catch (e) {
    logger.error(`Invalid URL format: ${url}`);
    return {
      success: false,
      isError: true,
      content: [{ type: "text", text: "Invalid URL format. Please provide a valid URL with http or https protocol" }],
      error: "Invalid URL format"
    };
  }

  // 验证并转换参数
  const timeout = args?.timeout !== undefined ? Number(args.timeout) : 30000;
  if (isNaN(timeout) || timeout <= 0) {
    logger.warn(`Invalid timeout value: ${args?.timeout}, using default 30000ms`);
  }
  
  const waitUntilOptions = ["load", "domcontentloaded", "networkidle", "commit"];
  const waitUntil = String(args?.waitUntil || "load");
  if (!waitUntilOptions.includes(waitUntil)) {
    logger.warn(`Invalid waitUntil value: ${waitUntil}, using default "load"`);
  }
  
  const maxLength = args?.maxLength !== undefined ? Number(args.maxLength) : 0;
  if (isNaN(maxLength) || maxLength < 0) {
    logger.warn(`Invalid maxLength value: ${args?.maxLength}, using no limit`);
  }
  
  const navigationTimeout = args?.navigationTimeout !== undefined ? Number(args.navigationTimeout) : 10000;
  if (isNaN(navigationTimeout) || navigationTimeout <= 0) {
    logger.warn(`Invalid navigationTimeout value: ${args?.navigationTimeout}, using default 10000ms`);
  }
  
  const options: FetchOptions = {
    timeout: isNaN(timeout) || timeout <= 0 ? 30000 : timeout,
    waitUntil: waitUntilOptions.includes(waitUntil) ? waitUntil as "load" | "domcontentloaded" | "networkidle" | "commit" : "load",
    extractContent: args?.extractContent !== false,
    maxLength: isNaN(maxLength) || maxLength < 0 ? 0 : maxLength,
    returnHtml: args?.returnHtml === true,
    waitForNavigation: args?.waitForNavigation === true,
    navigationTimeout: isNaN(navigationTimeout) || navigationTimeout <= 0 ? 10000 : navigationTimeout,
    disableMedia: args?.disableMedia !== false,
    debug: args?.debug === true,
  };

  // 记录开始时间，用于性能监控
  const startTime = Date.now();
  
  // 安全检查：限制URL长度，防止恶意输入
  if (url.length > 2000) {
    logger.warn(`URL length (${url.length}) exceeds recommended maximum (2000)`);
  }
  
  // 创建服务实例
  const browserService = FetcherFactory.createBrowserService(options);
  const processor = FetcherFactory.createContentProcessor(options, "[FetchURL]");
  let browser: Browser | null = null;
  let page: Page | null = null;
  
  // 记录操作
  logger.info(`[FetchURL] Starting fetch for URL: ${url.substring(0, 100)}${url.length > 100 ? '...' : ''}`);

  if (browserService.isInDebugMode()) {
    logger.debug(`Debug mode enabled for URL: ${url}`);
  }

  try {
    // Create a stealth browser with anti-detection measures
    browser = await browserService.createBrowser();
    if (!browser) {
      throw new Error("Failed to create browser instance");
    }

    // Create a stealth browser context
    const { context, viewport } = await browserService.createContext(browser);
    if (!context) {
      throw new Error("Failed to create browser context");
    }

    // Create a new page with human-like behavior
    page = await browserService.createPage(context, viewport);
    if (!page) {
      throw new Error("Failed to create page");
    }

    // Set timeout for the entire operation
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(`Operation timed out after ${options.timeout}ms`)), options.timeout);
    });

    // Process page content with timeout
    const result = await Promise.race([
      processor.processPageContent(page, url),
      timeoutPromise
    ]);

    // Validate result
    if (!result || !result.content || !Array.isArray(result.content) || result.content.length === 0) {
      throw new Error("Invalid content received from processor");
    }

    // 应用内容长度限制
    let finalContent = result.content[0].text;
    if (options.maxLength > 0 && finalContent.length > options.maxLength) {
      logger.info(`Content length (${finalContent.length}) exceeds maxLength (${options.maxLength}), truncating...`);
      finalContent = finalContent.substring(0, options.maxLength) + "\n\n[Content truncated due to length limit]";
    }

    return {
      success: true,
      content: [{ type: "text", text: finalContent }]
    };
  } catch (e: any) {
    const msg = e?.message || String(e);
    logger.error(`[FetchURL] Failed to fetch URL: ${msg}`);
    
    // Common helpful hint if Playwright browser is not installed
    let hint = "";
    if (msg.includes("playwright") || msg.includes("chromium")) {
      hint = " Hint: ensure Chromium is installed by running: npx playwright install chromium";
    } else if (msg.includes("timeout")) {
      hint = " Consider increasing the timeout value or checking your network connection.";
    } else if (msg.includes("ECONNREFUSED") || msg.includes("ENOTFOUND")) {
      hint = " The server might be down or the URL might be incorrect.";
    }
    
    return {
      success: false,
      isError: true,
      content: [{ type: "text", text: `Failed to fetch URL: ${msg}.${hint}` }],
      error: msg
    };
  } finally {
    try {
      // Clean up resources
      if (browser || page) {
        await browserService.cleanup(browser, page);
      }
      
      if (browserService.isInDebugMode()) {
        logger.debug(`Browser and page kept open for debugging. URL: ${url}`);
      }
      
      // 记录执行时间
      const executionTime = Date.now() - startTime;
      logger.info(`[FetchURL] Completed fetch for URL: ${url.substring(0, 100)}${url.length > 100 ? '...' : ''} in ${executionTime}ms`);
      
      // 性能警告
      if (executionTime > options.timeout * 0.8) {
        logger.warn(`[FetchURL] Operation took ${executionTime}ms, which is more than 80% of the timeout (${options.timeout}ms)`);
      }
    } catch (cleanupError: any) {
      logger.error(`Error during cleanup: ${cleanupError?.message || String(cleanupError)}`);
    }
  }
}
