import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";
import { FetcherFactory } from "../services/fetcherFactory.js";
import { FetchOptions, FetchResult } from "../types/index.js";
import { logger } from "../utils/logger.js";

/**
 * Tool definition for fetch_url
 */
export const fetchUrlTool = {
  name: "fetch_url",
  // description: "Retrieve webpage content from a specified URL",
  description: "从指定网址精准提炼网页骨架，全面解析页面结构，输出条理清晰的标题、段落、链接、图片、表格及元数据，适用于内容洞察、网页抓取与高效生成结构化网页快照，支持 HTML 解析并以层次分明的清爽格式呈现。",
  inputSchema: {
    type: "object",
    properties: {
      url: {
        type: "string",
        description: "URL to fetch. Make sure to include the schema (http:// or https:// if not defined, preferring https for most cases)",
      },
      timeout: {
        type: "number",
        description:
          "Page loading timeout in milliseconds, default is 30000 (30 seconds)",
      },
      waitUntil: {
        type: "string",
        description:
          "Specifies when navigation is considered complete, options: 'load', 'domcontentloaded', 'networkidle', 'commit', default is 'load'",
      },
      extractContent: {
        type: "boolean",
        description:
          "Whether to intelligently extract the main content, default is true",
      },
      maxLength: {
        type: "number",
        description:
          "Maximum length of returned content (in characters), default is no limit",
      },
      returnHtml: {
        type: "boolean",
        description:
          "Whether to return HTML content instead of Markdown, default is false",
      },
      waitForNavigation: {
        type: "boolean",
        description:
          "Whether to wait for additional navigation after initial page load (useful for sites with anti-bot verification), default is false",
      },
      navigationTimeout: {
        type: "number",
        description:
          "Maximum time to wait for additional navigation in milliseconds, default is 10000 (10 seconds)",
      },
      disableMedia: {
        type: "boolean",
        description:
          "Whether to disable media resources (images, stylesheets, fonts, media), default is true",
      },
      debug: {
        type: "boolean",
        description:
          "Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified",
      },
    },
    required: ["url"],
  },
};

/**
 * 验证URL格式
 */
function validateUrl(url: string): { isValid: boolean; error?: string } {
  if (!url || typeof url !== 'string') {
    return { isValid: false, error: "URL parameter is required" };
  }

  try {
    const urlObj = new URL(url);
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      return { isValid: false, error: "URL must use http or https protocol" };
    }
    return { isValid: true };
  } catch {
    return { isValid: false, error: "Invalid URL format. Please provide a valid URL with http or https protocol" };
  }
}

/**
 * 解析和验证选项
 */
function parseOptions(args: any): FetchOptions {
  const timeout = args?.timeout !== undefined ? Number(args.timeout) : 30000;
  const waitUntil = String(args?.waitUntil || "load");
  const maxLength = args?.maxLength !== undefined ? Number(args.maxLength) : 0;
  const navigationTimeout = args?.navigationTimeout !== undefined ? Number(args.navigationTimeout) : 10000;

  const waitUntilOptions = ["load", "domcontentloaded", "networkidle", "commit"];

  return {
    timeout: isNaN(timeout) || timeout <= 0 ? 30000 : timeout,
    waitUntil: waitUntilOptions.includes(waitUntil) ? waitUntil as FetchOptions['waitUntil'] : "load",
    extractContent: args?.extractContent !== false,
    maxLength: isNaN(maxLength) || maxLength < 0 ? 0 : maxLength,
    returnHtml: args?.returnHtml === true,
    waitForNavigation: args?.waitForNavigation === true,
    navigationTimeout: isNaN(navigationTimeout) || navigationTimeout <= 0 ? 10000 : navigationTimeout,
    disableMedia: args?.disableMedia !== false,
    debug: args?.debug === true,
  };
}

/**
 * Implementation of the fetch_url tool
 */
export async function fetchUrl(args: any): Promise<FetchResult> {
  const url = String(args?.url || "");

  // 验证URL
  const urlValidation = validateUrl(url);
  if (!urlValidation.isValid) {
    logger.error(`URL validation failed: ${urlValidation.error}`);
    return {
      success: false,
      isError: true,
      content: [{ type: "text", text: urlValidation.error! }],
      error: urlValidation.error
    };
  }

  // 解析选项
  const options = parseOptions(args);

  logger.info(`[FetchURL] Starting fetch for URL: ${url}`);

  // 创建服务实例
  const browserService = FetcherFactory.createBrowserService(options);
  const processor = FetcherFactory.createContentProcessor(options, "[FetchURL]");
  let browser: Browser | null = null;
  let page: Page | null = null;

  try {
    // 创建浏览器和页面
    browser = await browserService.createBrowser();
    const { context, viewport } = await browserService.createContext(browser);
    page = await browserService.createPage(context, viewport);

    // 处理页面内容
    const result = await processor.processPageContent(page, url);

    // 验证结果
    if (!result?.content?.length) {
      throw new Error("No content received from processor");
    }

    // 应用长度限制
    let finalContent = result.content[0].text;
    if (options.maxLength > 0 && finalContent.length > options.maxLength) {
      logger.info(`Content truncated from ${finalContent.length} to ${options.maxLength} characters`);
      finalContent = finalContent.substring(0, options.maxLength) + "\n\n[Content truncated due to length limit]";
    }

    logger.info(`[FetchURL] Successfully fetched URL: ${url}`);
    return {
      success: true,
      content: [{ type: "text", text: finalContent }]
    };
  } catch (error: any) {
    const message = error?.message || String(error);
    logger.error(`[FetchURL] Failed to fetch URL: ${message}`);

    // 生成有用的提示
    let hint = "";
    if (message.includes("playwright") || message.includes("chromium")) {
      hint = " Hint: ensure Chromium is installed by running: npx playwright install chromium";
    }

    return {
      success: false,
      isError: true,
      content: [{ type: "text", text: `Failed to fetch URL: ${message}${hint}` }],
      error: message
    };
  } finally {
    // 清理资源
    try {
      await browserService.cleanup(browser, page);
    } catch (cleanupError: any) {
      logger.error(`Cleanup error: ${cleanupError?.message || String(cleanupError)}`);
    }
  }
}
