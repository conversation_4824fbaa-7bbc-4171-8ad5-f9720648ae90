import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright";
import { FetcherFactory } from "../services/fetcherFactory.js";
import { FetchOptions, FetchResult} from "../types/index.js";
import { logger } from "../utils/logger.js";
import { IContentFetcher } from "../interfaces/fetcher.js";

/**
 * Tool definition for fetch_url
 */
export const fetchUrlTool = {
  name: "fetch_url",
  // description: "Retrieve webpage content from a specified URL",
  description: "从指定网址精准提炼网页骨架，全面解析页面结构，输出条理清晰的标题、段落、链接、图片、表格及元数据，适用于内容洞察、网页抓取与高效生成结构化网页快照，支持 HTML 解析并以层次分明的清爽格式呈现。",
  inputSchema: {
    type: "object",
    properties: {
      url: {
        type: "string",
        description: "URL to fetch. Make sure to include the schema (http:// or https:// if not defined, preferring https for most cases)",
      },
      timeout: {
        type: "number",
        description:
          "Page loading timeout in milliseconds, default is 30000 (30 seconds)",
      },
      waitUntil: {
        type: "string",
        description:
          "Specifies when navigation is considered complete, options: 'load', 'domcontentloaded', 'networkidle', 'commit', default is 'load'",
      },
      extractContent: {
        type: "boolean",
        description:
          "Whether to intelligently extract the main content, default is true",
      },
      maxLength: {
        type: "number",
        description:
          "Maximum length of returned content (in characters), default is no limit",
      },
      returnHtml: {
        type: "boolean",
        description:
          "Whether to return HTML content instead of Markdown, default is false",
      },
      waitForNavigation: {
        type: "boolean",
        description:
          "Whether to wait for additional navigation after initial page load (useful for sites with anti-bot verification), default is false",
      },
      navigationTimeout: {
        type: "number",
        description:
          "Maximum time to wait for additional navigation in milliseconds, default is 10000 (10 seconds)",
      },
      disableMedia: {
        type: "boolean",
        description:
          "Whether to disable media resources (images, stylesheets, fonts, media), default is true",
      },
      debug: {
        type: "boolean",
        description:
          "Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified",
      },
    },
    required: ["url"],
  },
};

/**
 * Implementation of the fetch_url tool
 */
export async function fetchUrl(args: any): Promise<FetchResult> {
  const url = String(args?.url || "");
  if (!url) {
    logger.error(`URL parameter missing`);
    return {
      isError: true,
      content: [{ type: "text", text: "URL parameter is required" }]
    };
  }

  const options: FetchOptions = {
    timeout: Number(args?.timeout) || 30000,
    waitUntil: String(args?.waitUntil || "load") as
      | "load"
      | "domcontentloaded"
      | "networkidle"
      | "commit",
    extractContent: args?.extractContent !== false,
    maxLength: Number(args?.maxLength) || 0,
    returnHtml: args?.returnHtml === true,
    waitForNavigation: args?.waitForNavigation === true,
    navigationTimeout: Number(args?.navigationTimeout) || 10000,
    disableMedia: args?.disableMedia !== false,
    debug: args?.debug,
  };

  // Create services using factory
  const browserService = FetcherFactory.createBrowserService(options);
  const processor = FetcherFactory.createContentProcessor(options, "[FetchURL]");
  let browser: Browser | null = null;
  let page: Page | null = null;

  if (browserService.isInDebugMode()) {
    logger.debug(`Debug mode enabled for URL: ${url}`);
  }

  try {
    // Create a stealth browser with anti-detection measures
    browser = await browserService.createBrowser();

    // Create a stealth browser context
    const { context, viewport } = await browserService.createContext(browser);

    // Create a new page with human-like behavior
    page = await browserService.createPage(context, viewport);

    // Process page content
    const result = await processor.processPageContent(page, url);

    return {
      content: [{ type: "text", text: result.content }]
    };
  } catch (e: any) {
    const msg = e?.message || String(e);
    logger.error(`[FetchURL] Failed to fetch URL: ${msg}`);
    // Common helpful hint if Playwright browser is not installed
    const hint = msg.includes("playwright") || msg.includes("chromium")
      ? " Hint: ensure Chromium is installed by running: npx playwright install chromium"
      : "";
    return {
      isError: true,
      content: [{ type: "text", text: `Failed to fetch URL: ${msg}.${hint}` }]
    };
  } finally {
    // Clean up resources
    await browserService.cleanup(browser, page);

    if (browserService.isInDebugMode()) {
      logger.debug(`Browser and page kept open for debugging. URL: ${url}`);
    }
  }
}
