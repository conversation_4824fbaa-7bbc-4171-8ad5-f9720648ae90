import { FetcherFactory } from "../services/fetcherFactory.js";
import { FetchOptions, FetchResult } from "../types/index.js";
import { logger } from "../utils/logger.js";

/**
 * Tool definition for fetch_urls
 */
export const fetchUrlsTool = {
  name: "fetch_urls",
  // description: "Retrieve web page content from multiple specified URLs",
  // description: "Extracts structured content from given URLs/Hyper Links, providing a comprehensive analysis of the page structure. " +
  //   "Returns organized data including headings, paragraphs, links, images, tables, and metadata. " +
  //   "Ideal for content analysis, web scraping, and generating structured representations of web pages. " +
  //   "Supports HTML parsing with clean, hierarchical output format.",
  description: "从从多个指定网址精准提炼网页骨架，全面解析页面结构，输出条理清晰的标题、段落、链接、图片、表格及元数据，适用于内容洞察、网页抓取与高效生成结构化网页快照，支持 HTML 解析并以层次分明的清爽格式呈现。",
  inputSchema: {
    type: "object",
    properties: {
      urls: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of URLs to fetch",
      },
      timeout: {
        type: "number",
        description:
          "Page loading timeout in milliseconds, default is 30000 (30 seconds)",
      },
      waitUntil: {
        type: "string",
        description:
          "Specifies when navigation is considered complete, options: 'load', 'domcontentloaded', 'networkidle', 'commit', default is 'load'",
      },
      extractContent: {
        type: "boolean",
        description:
          "Whether to intelligently extract the main content, default is true",
      },
      maxLength: {
        type: "number",
        description:
          "Maximum length of returned content (in characters), default is no limit",
      },
      returnHtml: {
        type: "boolean",
        description:
          "Whether to return HTML content instead of Markdown, default is false",
      },
      waitForNavigation: {
        type: "boolean",
        description:
          "Whether to wait for additional navigation after initial page load (useful for sites with anti-bot verification), default is false",
      },
      navigationTimeout: {
        type: "number",
        description:
          "Maximum time to wait for additional navigation in milliseconds, default is 10000 (10 seconds)",
      },
      disableMedia: {
        type: "boolean",
        description:
          "Whether to disable media resources (images, stylesheets, fonts, media), default is true",
      },
      debug: {
        type: "boolean",
        description:
          "Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified",
      },
    },
    required: ["urls"],
  },
};

/**
 * 验证URLs数组
 */
function validateUrls(urls: any): { isValid: boolean; error?: string } {
  if (!urls || !Array.isArray(urls) || urls.length === 0) {
    return { isValid: false, error: "URLs parameter is required and must be a non-empty array" };
  }

  for (const url of urls) {
    if (!url || typeof url !== 'string') {
      return { isValid: false, error: "All URLs must be valid strings" };
    }

    try {
      const urlObj = new URL(url);
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { isValid: false, error: `Invalid protocol in URL: ${url}` };
      }
    } catch {
      return { isValid: false, error: `Invalid URL format: ${url}` };
    }
  }

  return { isValid: true };
}

/**
 * 解析选项
 */
function parseOptions(args: any): FetchOptions {
  const timeout = args?.timeout !== undefined ? Number(args.timeout) : 30000;
  const waitUntil = String(args?.waitUntil || "load");
  const maxLength = args?.maxLength !== undefined ? Number(args.maxLength) : 0;
  const navigationTimeout = args?.navigationTimeout !== undefined ? Number(args.navigationTimeout) : 10000;

  const waitUntilOptions = ["load", "domcontentloaded", "networkidle", "commit"];

  return {
    timeout: isNaN(timeout) || timeout <= 0 ? 30000 : timeout,
    waitUntil: waitUntilOptions.includes(waitUntil) ? waitUntil as FetchOptions['waitUntil'] : "load",
    extractContent: args?.extractContent !== false,
    maxLength: isNaN(maxLength) || maxLength < 0 ? 0 : maxLength,
    returnHtml: args?.returnHtml === true,
    waitForNavigation: args?.waitForNavigation === true,
    navigationTimeout: isNaN(navigationTimeout) || navigationTimeout <= 0 ? 10000 : navigationTimeout,
    disableMedia: args?.disableMedia !== false,
    debug: args?.debug === true,
  };
}

/**
 * Implementation of the fetch_urls tool
 */
export async function fetchUrls(args: any): Promise<FetchResult> {
  const urls = args?.urls || [];

  // 验证URLs
  const urlValidation = validateUrls(urls);
  if (!urlValidation.isValid) {
    logger.error(`URLs validation failed: ${urlValidation.error}`);
    return {
      success: false,
      content: [{ type: "text", text: urlValidation.error! }],
      error: urlValidation.error
    };
  }

  // 解析选项
  const options = parseOptions(args);

  logger.info(`[FetchURLs] Starting batch fetch for ${urls.length} URLs`);

  // 创建服务实例
  const browserService = FetcherFactory.createBrowserService(options);
  const processor = FetcherFactory.createContentProcessor(options, "[FetchURLs]");

  let browser = null;
  try {
    // 创建浏览器和上下文
    browser = await browserService.createBrowser();
    const { context, viewport } = await browserService.createContext(browser);

    // 并行处理所有URLs
    const results = await Promise.all(
      urls.map(async (url: string, index: number) => {
        let page = null;
        try {
          page = await browserService.createPage(context, viewport);
          const result = await processor.processPageContent(page, url);

          if (!result?.content?.length) {
            throw new Error("No content received");
          }

          return { index, content: result.content[0].text };
        } catch (error: any) {
          const message = error?.message || String(error);
          logger.error(`[FetchURLs] Failed to fetch ${url}: ${message}`);
          return {
            index,
            content: `Title: Error\nURL: ${url}\nContent:\n\n<error>Failed to retrieve web page content: ${message}</error>`
          };
        } finally {
          if (page && !browserService.isInDebugMode()) {
            try {
              await page.close();
            } catch (e: any) {
              logger.error(`Failed to close page: ${e.message}`);
            }
          }
        }
      })
    );

    // 排序并合并结果
    results.sort((a, b) => a.index - b.index);
    const combinedResults = results
      .map((result, i) => `[webpage ${i + 1} begin]\n${result.content}\n[webpage ${i + 1} end]`)
      .join("\n\n");

    logger.info(`[FetchURLs] Successfully processed ${urls.length} URLs`);
    return {
      success: true,
      content: [{ type: "text", text: combinedResults }]
    };
  } catch (error: any) {
    const message = error?.message || String(error);
    logger.error(`[FetchURLs] Batch fetch failed: ${message}`);

    let hint = "";
    if (message.includes("playwright") || message.includes("chromium")) {
      hint = " Hint: ensure Chromium is installed by running: npx playwright install chromium";
    }

    return {
      success: false,
      content: [{ type: "text", text: `Failed to fetch URLs: ${message}${hint}` }],
      error: message
    };
  } finally {
    // 清理浏览器资源
    try {
      if (browser && !browserService.isInDebugMode()) {
        await browser.close();
      }
    } catch (cleanupError: any) {
      logger.error(`Browser cleanup error: ${cleanupError?.message || String(cleanupError)}`);
    }
  }
}
