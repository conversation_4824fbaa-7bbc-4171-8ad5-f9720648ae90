import { <PERSON>rowser, <PERSON> } from "playwright";
import { FetcherFactory } from "../services/fetcherFactory.js";
import { FetchOptions, FetchResult } from "../types/index.js";
import { logger } from "../utils/logger.js";
import { IContentFetcher } from "../interfaces/fetcher.js";

/**
 * Tool definition for fetch_urls
 */
export const fetchUrlsTool = {
  name: "fetch_urls",
  // description: "Retrieve web page content from multiple specified URLs",
  // description: "Extracts structured content from given URLs/Hyper Links, providing a comprehensive analysis of the page structure. " +
  //   "Returns organized data including headings, paragraphs, links, images, tables, and metadata. " +
  //   "Ideal for content analysis, web scraping, and generating structured representations of web pages. " +
  //   "Supports HTML parsing with clean, hierarchical output format.",
  description: "从从多个指定网址精准提炼网页骨架，全面解析页面结构，输出条理清晰的标题、段落、链接、图片、表格及元数据，适用于内容洞察、网页抓取与高效生成结构化网页快照，支持 HTML 解析并以层次分明的清爽格式呈现。",
  inputSchema: {
    type: "object",
    properties: {
      urls: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of URLs to fetch",
      },
      timeout: {
        type: "number",
        description:
          "Page loading timeout in milliseconds, default is 30000 (30 seconds)",
      },
      waitUntil: {
        type: "string",
        description:
          "Specifies when navigation is considered complete, options: 'load', 'domcontentloaded', 'networkidle', 'commit', default is 'load'",
      },
      extractContent: {
        type: "boolean",
        description:
          "Whether to intelligently extract the main content, default is true",
      },
      maxLength: {
        type: "number",
        description:
          "Maximum length of returned content (in characters), default is no limit",
      },
      returnHtml: {
        type: "boolean",
        description:
          "Whether to return HTML content instead of Markdown, default is false",
      },
      waitForNavigation: {
        type: "boolean",
        description:
          "Whether to wait for additional navigation after initial page load (useful for sites with anti-bot verification), default is false",
      },
      navigationTimeout: {
        type: "number",
        description:
          "Maximum time to wait for additional navigation in milliseconds, default is 10000 (10 seconds)",
      },
      disableMedia: {
        type: "boolean",
        description:
          "Whether to disable media resources (images, stylesheets, fonts, media), default is true",
      },
      debug: {
        type: "boolean",
        description:
          "Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified",
      },
    },
    required: ["urls"],
  },
};

/**
 * Implementation of the fetch_urls tool
 */
export async function fetchUrls(args: any) {
  const urls = (args?.urls as string[]) || [];
  if (!urls || !Array.isArray(urls) || urls.length === 0) {
    return {
      isError: true,
      content: [{ type: "text", text: "URLs parameter is required and must be a non-empty array" }]
    };
  }

  const options: FetchOptions = {
    timeout: Number(args?.timeout) || 30000,
    waitUntil: String(args?.waitUntil || "load") as
      | "load"
      | "domcontentloaded"
      | "networkidle"
      | "commit",
    extractContent: args?.extractContent !== false,
    maxLength: Number(args?.maxLength) || 0,
    returnHtml: args?.returnHtml === true,
    waitForNavigation: args?.waitForNavigation === true,
    navigationTimeout: Number(args?.navigationTimeout) || 10000,
    disableMedia: args?.disableMedia !== false,
    debug: args?.debug,
  };

  // Create services using factory
  const browserService = FetcherFactory.createBrowserService(options);

  if (browserService.isInDebugMode()) {
    logger.debug(`Debug mode enabled for URLs: ${urls.join(", ")}`);
  }

  let browser: Browser | null = null;
  try {
    // Create a stealth browser with anti-detection measures
    browser = await browserService.createBrowser();
    
    // Create a stealth browser context
    const { context, viewport } = await browserService.createContext(browser);

    const processor = FetcherFactory.createContentProcessor(options, "[FetchURLs]");

    const results = await Promise.all(
      urls.map(async (url, index) => {
        // Create a new page with human-like behavior
        const page = await browserService.createPage(context, viewport);

        try {
          const result = await processor.processPageContent(page, url);
          return { index, ...result } as FetchResult;
        } catch (e: any) {
          const msg = e?.message || String(e);
          logger.error(`[FetchURLs] Failed to fetch ${url}: ${msg}`);
          // Ensure we always return a result entry so one failure doesn't fail the whole batch
          const content = `Title: Error\nURL: ${url}\nContent:\n\n<error>Failed to retrieve web page content: ${msg}</error>`;
          return { index, success: false, content } as FetchResult;
        } finally {
          if (!browserService.isInDebugMode()) {
            await page
              .close()
              .catch((e) => logger.error(`Failed to close page: ${e.message}`));
          } else {
            logger.debug(`Page kept open for debugging. URL: ${url}`);
          }
        }
      })
    );

    results.sort((a, b) => (a.index || 0) - (b.index || 0));
    const combinedResults = results
      .map(
        (result, i) =>
          `[webpage ${i + 1} begin]\n${result.content}\n[webpage ${i + 1} end]`
      )
      .join("\n\n");

    return {
      content: [{ type: "text", text: combinedResults }],
    };
  } catch (e: any) {
    const msg = e?.message || String(e);
    logger.error(`[FetchURLs] Top-level failure: ${msg}`);
    const hint = /playwright|chromium/i.test(msg)
      ? " Hint: ensure Chromium is installed by running: npx playwright install chromium"
      : "";
    return {
      isError: true,
      content: [{ type: "text", text: `Failed to fetch URLs: ${msg}.${hint}` }]
    };
  } finally {
    // Clean up browser resources
    if (!browserService.isInDebugMode()) {
      if (browser)
        await browser
          .close()
          .catch((e) => logger.error(`Failed to close browser: ${e.message}`));
    } else {
      logger.debug(`Browser kept open for debugging. URLs: ${urls.join(", ")}`);
    }
  }
}
