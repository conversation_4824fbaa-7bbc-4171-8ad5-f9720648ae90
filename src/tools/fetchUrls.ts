import { FetcherFactory } from "../services/fetcherFactory.js";
import { FetchOptions, FetchResult } from "../types/index.js";
import { logger } from "../utils/logger.js";
import { IEnhancedContentFetcher } from "../interfaces/enhanced-fetcher.js";

/**
 * Tool definition for fetch_urls
 */
export const fetchUrlsTool = {
  name: "fetch_urls",
  // description: "Retrieve web page content from multiple specified URLs",
  // description: "Extracts structured content from given URLs/Hyper Links, providing a comprehensive analysis of the page structure. " +
  //   "Returns organized data including headings, paragraphs, links, images, tables, and metadata. " +
  //   "Ideal for content analysis, web scraping, and generating structured representations of web pages. " +
  //   "Supports HTML parsing with clean, hierarchical output format.",
  description: "从从多个指定网址精准提炼网页骨架，全面解析页面结构，输出条理清晰的标题、段落、链接、图片、表格及元数据，适用于内容洞察、网页抓取与高效生成结构化网页快照，支持 HTML 解析并以层次分明的清爽格式呈现。",
  inputSchema: {
    type: "object",
    properties: {
      urls: {
        type: "array",
        items: {
          type: "string",
        },
        description: "Array of URLs to fetch",
      },
      timeout: {
        type: "number",
        description:
          "Page loading timeout in milliseconds, default is 30000 (30 seconds)",
      },
      waitUntil: {
        type: "string",
        description:
          "Specifies when navigation is considered complete, options: 'load', 'domcontentloaded', 'networkidle', 'commit', default is 'load'",
      },
      extractContent: {
        type: "boolean",
        description:
          "Whether to intelligently extract the main content, default is true",
      },
      maxLength: {
        type: "number",
        description:
          "Maximum length of returned content (in characters), default is no limit",
      },
      returnHtml: {
        type: "boolean",
        description:
          "Whether to return HTML content instead of Markdown, default is false",
      },
      waitForNavigation: {
        type: "boolean",
        description:
          "Whether to wait for additional navigation after initial page load (useful for sites with anti-bot verification), default is false",
      },
      navigationTimeout: {
        type: "number",
        description:
          "Maximum time to wait for additional navigation in milliseconds, default is 10000 (10 seconds)",
      },
      disableMedia: {
        type: "boolean",
        description:
          "Whether to disable media resources (images, stylesheets, fonts, media), default is true",
      },
      debug: {
        type: "boolean",
        description:
          "Whether to enable debug mode (showing browser window), overrides the --debug command line flag if specified",
      },
    },
    required: ["urls"],
  },
};

/**
 * Implementation of the fetch_urls tool
 */
export async function fetchUrls(args: any): Promise<FetchResult> {
  const urls = (args?.urls as string[]) || [];
  if (!urls || !Array.isArray(urls) || urls.length === 0) {
    return {
      success: false,
      content: [{ type: "text", text: "URLs parameter is required and must be a non-empty array" }],
      error: "URLs parameter is required and must be a non-empty array"
    };
  }

  // Parse options from arguments
  const options: Partial<FetchOptions> = {
    timeout: args?.timeout ? Number(args.timeout) : undefined,
    waitUntil: args?.waitUntil ? String(args.waitUntil) as FetchOptions['waitUntil'] : undefined,
    extractContent: args?.extractContent !== undefined ? Boolean(args.extractContent) : undefined,
    maxLength: args?.maxLength ? Number(args.maxLength) : undefined,
    returnHtml: args?.returnHtml !== undefined ? Boolean(args.returnHtml) : undefined,
    waitForNavigation: args?.waitForNavigation !== undefined ? Boolean(args.waitForNavigation) : undefined,
    navigationTimeout: args?.navigationTimeout ? Number(args.navigationTimeout) : undefined,
    disableMedia: args?.disableMedia !== undefined ? Boolean(args.disableMedia) : undefined,
    debug: args?.debug !== undefined ? Boolean(args.debug) : undefined,
  };

  // Create enhanced content fetcher
  const fetcher = FetcherFactory.createEnhancedContentFetcher(
    {
      timeout: 30000,
      waitUntil: 'load',
      extractContent: true,
      maxLength: 0,
      returnHtml: false,
      waitForNavigation: false,
      navigationTimeout: 10000,
      disableMedia: true,
      debug: false,
    },
    "[FetchURLs]"
  );

  try {
    logger.info(`[FetchURLs] Starting batch fetch for ${urls.length} URLs`);
    const result = await fetcher.fetchUrls(urls, options);

    if (result.success) {
      logger.info(`[FetchURLs] Successfully fetched ${urls.length} URLs`);
    } else {
      logger.error(`[FetchURLs] Failed to fetch URLs, Error: ${result.error}`);
    }

    return result;
  } catch (error: any) {
    const msg = error?.message || String(error);
    logger.error(`[FetchURLs] Unexpected error: ${msg}`);

    return {
      success: false,
      content: [{ type: "text", text: `Unexpected error: ${msg}` }],
      error: msg
    };
  } finally {
    // Clean up resources
    await fetcher.cleanup();
  }
}
