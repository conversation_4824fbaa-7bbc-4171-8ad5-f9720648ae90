# 增强架构使用指南

## 概述

新的增强架构提供了更好的模块化、错误处理和资源管理。本文档展示如何使用新的接口和服务。

## 核心组件

### 1. 增强的内容抓取器 (IEnhancedContentFetcher)

```typescript
import { FetcherFactory } from "../services/fetcherFactory.js";

// 创建增强的内容抓取器
const fetcher = FetcherFactory.createEnhancedContentFetcher({
  timeout: 30000,
  waitUntil: 'load',
  extractContent: true,
  maxLength: 0,
  returnHtml: false,
  waitForNavigation: false,
  navigationTimeout: 10000,
  disableMedia: true,
  debug: false,
}, "[MyApp]");

// 抓取单个URL
const result = await fetcher.fetchUrl("https://example.com", {
  timeout: 60000,
  extractContent: false
});

// 批量抓取
const batchResult = await fetcher.fetchUrls([
  "https://example1.com",
  "https://example2.com"
], {
  returnHtml: true
});

// 清理资源
await fetcher.cleanup();
```

### 2. 配置管理器 (IConfigManager)

```typescript
import { ConfigManager } from "../services/configManager.js";

const configManager = new ConfigManager();

// 设置默认配置
configManager.setDefaultOptions({
  timeout: 45000,
  extractContent: true
});

// 合并配置
const mergedOptions = configManager.merge({
  timeout: 60000, // 覆盖默认值
  returnHtml: true // 新增配置
});

// 验证配置
const errors = configManager.validateOptions({
  timeout: -1000 // 无效值
});
```

### 3. 错误处理器 (IErrorHandler)

```typescript
import { ErrorHandler } from "../services/errorHandler.js";

const errorHandler = new ErrorHandler({
  maxRetries: 5,
  retryDelay: 2000,
  backoffMultiplier: 1.5
});

// 检查是否应该重试
const shouldRetry = errorHandler.shouldRetry(error, currentRetryCount);

// 计算重试延迟
const delay = errorHandler.calculateRetryDelay(retryCount);

// 处理错误
const errorResult = await errorHandler.handle(error, {
  url: "https://example.com",
  options: fetchOptions,
  retryCount: 2,
  operation: 'fetchUrl'
});
```

### 4. 资源管理器 (IResourceManager)

```typescript
import { ResourceManager } from "../services/resourceManager.js";
import { BrowserService } from "../services/browserService.js";

const browserService = new BrowserService(options);
const resourceManager = new ResourceManager(browserService, options);

// 获取页面实例
const page = await resourceManager.getPage("https://example.com");

// 使用页面...

// 释放页面
await resourceManager.releasePage(page);

// 清理所有资源
await resourceManager.cleanup();

// 获取资源状态
const status = resourceManager.getResourceStatus();
console.log(`当前页面数量: ${status.pageCount}`);
```

## 架构优势

### 1. 代码重用
- 消除了 fetchUrl 和 fetchUrls 之间的重复代码
- 统一的配置管理和错误处理

### 2. 更好的错误处理
- 统一的错误处理策略
- 智能重试机制
- 用户友好的错误信息

### 3. 资源管理
- 自动资源清理
- 调试模式支持
- 资源状态监控

### 4. 可扩展性
- 接口驱动的设计
- 工厂模式便于扩展
- 策略模式支持不同抓取策略

## 迁移指南

### 从旧版本迁移

旧代码：
```typescript
// 旧的实现方式
const browserService = FetcherFactory.createBrowserService(options);
const processor = FetcherFactory.createContentProcessor(options, "[FetchURL]");
let browser = null;
let page = null;

try {
  browser = await browserService.createBrowser();
  const { context, viewport } = await browserService.createContext(browser);
  page = await browserService.createPage(context, viewport);
  const result = await processor.processPageContent(page, url);
  // 处理结果...
} finally {
  await browserService.cleanup(browser, page);
}
```

新代码：
```typescript
// 新的实现方式
const fetcher = FetcherFactory.createEnhancedContentFetcher(defaultOptions, "[FetchURL]");

try {
  const result = await fetcher.fetchUrl(url, customOptions);
  // 处理结果...
} finally {
  await fetcher.cleanup();
}
```

### 配置变更

旧的配置方式：
```typescript
const options: FetchOptions = {
  timeout: Number(args?.timeout) || 30000,
  waitUntil: String(args?.waitUntil || "load"),
  extractContent: args?.extractContent !== false,
  // ... 更多配置
};
```

新的配置方式：
```typescript
const options: Partial<FetchOptions> = {
  timeout: args?.timeout ? Number(args.timeout) : undefined,
  waitUntil: args?.waitUntil ? String(args.waitUntil) as FetchOptions['waitUntil'] : undefined,
  extractContent: args?.extractContent !== undefined ? Boolean(args.extractContent) : undefined,
  // ... 只设置明确提供的配置
};
```

## 最佳实践

### 1. 资源管理
- 始终在 finally 块中调用 cleanup()
- 使用 try-catch-finally 模式确保资源释放

### 2. 错误处理
- 检查 result.success 来判断操作是否成功
- 使用 result.error 获取详细错误信息

### 3. 配置管理
- 只设置需要覆盖的配置项
- 使用配置验证确保参数正确性

### 4. 调试
- 使用 debug 选项启用浏览器可视化
- 查看资源状态进行问题诊断
