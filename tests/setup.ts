/**
 * Jest 测试环境设置
 */

// 设置测试超时
jest.setTimeout(30000);

// 模拟环境变量
process.env.NODE_ENV = 'test';

// 全局测试工具
global.testUtils = {
  // 创建测试用的URL
  createTestUrl: (path: string = '') => `https://httpbin.org${path}`,
  
  // 等待指定时间
  wait: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 验证URL格式
  isValidUrl: (url: string) => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
};

// 声明全局类型
declare global {
  var testUtils: {
    createTestUrl: (path?: string) => string;
    wait: (ms: number) => Promise<void>;
    isValidUrl: (url: string) => boolean;
  };
}
