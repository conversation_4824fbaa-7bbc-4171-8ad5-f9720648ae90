import { fetchUrls } from '../../src/tools/fetchUrls.js';

describe('fetchUrls', () => {
  describe('URLs validation', () => {
    it('should reject empty URLs array', async () => {
      const result = await fetchUrls({});
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('URLs parameter is required and must be a non-empty array');
    });

    it('should reject non-array URLs', async () => {
      const result = await fetchUrls({ urls: 'not-an-array' });
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('URLs parameter is required and must be a non-empty array');
    });

    it('should reject invalid URLs in array', async () => {
      const result = await fetchUrls({ 
        urls: ['https://example.com', 'invalid-url'] 
      });
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid URL format');
    });

    it('should reject non-HTTP protocols', async () => {
      const result = await fetchUrls({ 
        urls: ['https://example.com', 'ftp://example.com'] 
      });
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid protocol');
    });
  });

  describe('batch processing', () => {
    it('should process multiple valid URLs', async () => {
      const urls = [
        testUtils.createTestUrl('/html'),
        testUtils.createTestUrl('/json')
      ];
      
      const result = await fetchUrls({ 
        urls,
        timeout: 10000
      });
      
      expect(result.success).toBe(true);
      expect(result.content[0].text).toContain('[webpage 1 begin]');
      expect(result.content[0].text).toContain('[webpage 2 begin]');
      expect(result.content[0].text).toContain('[webpage 1 end]');
      expect(result.content[0].text).toContain('[webpage 2 end]');
    });

    it('should handle mixed success and failure', async () => {
      const urls = [
        testUtils.createTestUrl('/html'),
        'https://nonexistent-domain-12345.com'
      ];
      
      const result = await fetchUrls({ 
        urls,
        timeout: 5000
      });
      
      expect(result.success).toBe(true);
      expect(result.content[0].text).toContain('[webpage 1 begin]');
      expect(result.content[0].text).toContain('[webpage 2 begin]');
      expect(result.content[0].text).toContain('<error>');
    });

    it('should maintain URL order in results', async () => {
      const urls = [
        testUtils.createTestUrl('/html'),
        testUtils.createTestUrl('/json'),
        testUtils.createTestUrl('/xml')
      ];
      
      const result = await fetchUrls({ 
        urls,
        timeout: 10000
      });
      
      expect(result.success).toBe(true);
      
      const content = result.content[0].text;
      const webpage1Index = content.indexOf('[webpage 1 begin]');
      const webpage2Index = content.indexOf('[webpage 2 begin]');
      const webpage3Index = content.indexOf('[webpage 3 begin]');
      
      expect(webpage1Index).toBeLessThan(webpage2Index);
      expect(webpage2Index).toBeLessThan(webpage3Index);
    });
  });

  describe('options handling', () => {
    it('should apply options to all URLs', async () => {
      const urls = [
        testUtils.createTestUrl('/html'),
        testUtils.createTestUrl('/json')
      ];
      
      const result = await fetchUrls({ 
        urls,
        extractContent: false,
        returnHtml: true
      });
      
      expect(result.success).toBe(true);
      expect(result.content[0].text).toContain('<');
    });

    it('should handle maxLength option', async () => {
      const urls = [
        testUtils.createTestUrl('/html')
      ];
      
      const result = await fetchUrls({ 
        urls,
        maxLength: 100
      });
      
      expect(result.success).toBe(true);
      // 检查是否有截断标记
      if (result.content[0].text.length > 300) {
        expect(result.content[0].text).toContain('[Content truncated due to length limit]');
      }
    });
  });

  describe('error resilience', () => {
    it('should continue processing after individual failures', async () => {
      const urls = [
        'https://nonexistent-domain-12345.com',
        testUtils.createTestUrl('/html'),
        'https://another-nonexistent-domain-12345.com'
      ];
      
      const result = await fetchUrls({ 
        urls,
        timeout: 5000
      });
      
      expect(result.success).toBe(true);
      expect(result.content[0].text).toContain('<error>');
      expect(result.content[0].text).toContain('Title:');
    });
  });
});
