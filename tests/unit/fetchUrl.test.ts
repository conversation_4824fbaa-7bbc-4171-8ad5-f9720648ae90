import { fetchUrl } from '../../src/tools/fetchUrl.js';
import { FetchResult } from '../../src/types/index.js';

describe('fetchUrl', () => {
  describe('URL validation', () => {
    it('should reject empty URL', async () => {
      const result = await fetchUrl({});
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('URL parameter is required');
      expect(result.content[0].text).toContain('URL parameter is required');
    });

    it('should reject invalid URL format', async () => {
      const result = await fetchUrl({ url: 'invalid-url' });
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid URL format');
    });

    it('should reject non-HTTP protocols', async () => {
      const result = await fetchUrl({ url: 'ftp://example.com' });
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('URL must use http or https protocol');
    });

    it('should accept valid HTTP URLs', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/html'),
        timeout: 10000
      });
      
      expect(result.success).toBe(true);
      expect(result.content).toBeDefined();
      expect(result.content[0].text).toContain('Title:');
    });
  });

  describe('options parsing', () => {
    it('should use default options when not provided', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/html')
      });
      
      expect(result.success).toBe(true);
    });

    it('should handle custom timeout', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/html'),
        timeout: 5000
      });
      
      expect(result.success).toBe(true);
    });

    it('should handle extractContent option', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/html'),
        extractContent: false
      });
      
      expect(result.success).toBe(true);
      expect(result.content[0].text).toContain('Content:');
    });

    it('should handle maxLength option', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/html'),
        maxLength: 100
      });
      
      expect(result.success).toBe(true);
      if (result.content[0].text.length > 200) { // 考虑标题和URL部分
        expect(result.content[0].text).toContain('[Content truncated due to length limit]');
      }
    });
  });

  describe('error handling', () => {
    it('should handle network errors gracefully', async () => {
      const result = await fetchUrl({ 
        url: 'https://nonexistent-domain-12345.com',
        timeout: 5000
      });
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle timeout errors', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/delay/10'),
        timeout: 2000
      });
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('content processing', () => {
    it('should extract and format content correctly', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/html')
      });
      
      expect(result.success).toBe(true);
      expect(result.content[0].text).toMatch(/Title: .+/);
      expect(result.content[0].text).toMatch(/URL: https:\/\/httpbin\.org\/html/);
      expect(result.content[0].text).toContain('Content:');
    });

    it('should return HTML when requested', async () => {
      const result = await fetchUrl({ 
        url: testUtils.createTestUrl('/html'),
        returnHtml: true
      });
      
      expect(result.success).toBe(true);
      expect(result.content[0].text).toContain('<');
      expect(result.content[0].text).toContain('>');
    });
  });
});
