import { Server } from "@modelcontextprotocol/sdk/server/index.js";
import { tools, toolHandlers } from '../../src/tools/index.js';

describe('MCP Server Integration', () => {
  let server: Server;

  beforeEach(() => {
    server = new Server(
      {
        name: "browser-mcp",
        version: "0.1.0",
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );
  });

  describe('Tool Registration', () => {
    it('should export correct tools', () => {
      expect(tools).toBeDefined();
      expect(Array.isArray(tools)).toBe(true);
      expect(tools.length).toBe(2);
      
      const toolNames = tools.map(tool => tool.name);
      expect(toolNames).toContain('fetch_url');
      expect(toolNames).toContain('fetch_urls');
    });

    it('should have corresponding tool handlers', () => {
      expect(toolHandlers).toBeDefined();
      expect(toolHandlers['fetch_url']).toBeDefined();
      expect(toolHandlers['fetch_urls']).toBeDefined();
      expect(typeof toolHandlers['fetch_url']).toBe('function');
      expect(typeof toolHandlers['fetch_urls']).toBe('function');
    });

    it('should have valid tool schemas', () => {
      tools.forEach(tool => {
        expect(tool.name).toBeDefined();
        expect(tool.description).toBeDefined();
        expect(tool.inputSchema).toBeDefined();
        expect(tool.inputSchema.type).toBe('object');
        expect(tool.inputSchema.properties).toBeDefined();
        expect(tool.inputSchema.required).toBeDefined();
      });
    });
  });

  describe('Tool Execution', () => {
    it('should execute fetch_url tool successfully', async () => {
      const handler = toolHandlers['fetch_url'];
      const result = await handler({
        url: testUtils.createTestUrl('/html'),
        timeout: 10000
      });

      expect(result).toBeDefined();
      expect(result.content).toBeDefined();
      expect(Array.isArray(result.content)).toBe(true);
      expect(result.content.length).toBeGreaterThan(0);
      expect(result.content[0].type).toBe('text');
      expect(result.content[0].text).toBeDefined();
    });

    it('should execute fetch_urls tool successfully', async () => {
      const handler = toolHandlers['fetch_urls'];
      const result = await handler({
        urls: [
          testUtils.createTestUrl('/html'),
          testUtils.createTestUrl('/json')
        ],
        timeout: 10000
      });

      expect(result).toBeDefined();
      expect(result.content).toBeDefined();
      expect(Array.isArray(result.content)).toBe(true);
      expect(result.content.length).toBeGreaterThan(0);
      expect(result.content[0].type).toBe('text');
      expect(result.content[0].text).toContain('[webpage 1 begin]');
    });

    it('should handle tool errors gracefully', async () => {
      const handler = toolHandlers['fetch_url'];
      const result = await handler({
        url: 'invalid-url'
      });

      expect(result).toBeDefined();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.content[0].text).toContain('Invalid URL format');
    });
  });

  describe('Tool Schema Validation', () => {
    it('should validate fetch_url required parameters', () => {
      const fetchUrlTool = tools.find(tool => tool.name === 'fetch_url');
      expect(fetchUrlTool?.inputSchema.required).toContain('url');
    });

    it('should validate fetch_urls required parameters', () => {
      const fetchUrlsTool = tools.find(tool => tool.name === 'fetch_urls');
      expect(fetchUrlsTool?.inputSchema.required).toContain('urls');
    });

    it('should have proper parameter types', () => {
      const fetchUrlTool = tools.find(tool => tool.name === 'fetch_url');
      const properties = fetchUrlTool?.inputSchema.properties;
      
      expect(properties?.url.type).toBe('string');
      expect(properties?.timeout.type).toBe('number');
      expect(properties?.extractContent.type).toBe('boolean');
      expect(properties?.returnHtml.type).toBe('boolean');
    });
  });

  describe('Performance', () => {
    it('should complete single URL fetch within reasonable time', async () => {
      const startTime = Date.now();
      const handler = toolHandlers['fetch_url'];
      
      await handler({
        url: testUtils.createTestUrl('/html'),
        timeout: 10000
      });
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(15000); // 15秒内完成
    });

    it('should handle concurrent requests efficiently', async () => {
      const handler = toolHandlers['fetch_urls'];
      const startTime = Date.now();
      
      await handler({
        urls: [
          testUtils.createTestUrl('/html'),
          testUtils.createTestUrl('/json'),
          testUtils.createTestUrl('/xml')
        ],
        timeout: 10000
      });
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(20000); // 20秒内完成3个URL
    });
  });
});
