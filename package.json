{"name": "fetcher-mcp", "version": "0.3.0", "description": "MCP server for fetching web content using Playwright browser", "private": false, "type": "module", "bin": {"fetcher-mcp": "build/index.js"}, "files": ["build", "icon.svg"], "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "watch": "tsc --watch", "inspector": "npm run build && npx @modelcontextprotocol/inspector -- node build/index.js --debug", "install-browser": "npx playwright install chromium"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.10.2", "@mozilla/readability": "^0.5.0", "express": "^4.18.2", "jsdom": "^24.0.0", "playwright": "^1.42.1", "turndown": "^7.1.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/jsdom": "^21.1.6", "@types/node": "^20.17.24", "@types/turndown": "^5.0.4", "typescript": "^5.3.3"}, "main": "index.js", "keywords": ["mcp", "playwright", "web-scraping", "readability", "content-extraction"], "author": "", "license": "ISC"}