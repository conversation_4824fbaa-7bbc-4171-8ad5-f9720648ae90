#!/usr/bin/env node

/**
 * 基本功能测试脚本
 * 测试 fetchUrl 和 fetchUrls 工具的基本功能
 */

import { fetchUrl } from './build/tools/fetchUrl.js';
import { fetchUrls } from './build/tools/fetchUrls.js';

async function testFetchUrl() {
  console.log('Testing fetchUrl...');
  
  try {
    const result = await fetchUrl({
      url: 'https://httpbin.org/html',
      timeout: 10000,
      extractContent: true
    });
    
    console.log('fetchUrl result:', {
      success: result.success,
      hasContent: result.content && result.content.length > 0,
      contentLength: result.content?.[0]?.text?.length || 0,
      error: result.error
    });
    
    return result.success !== false;
  } catch (error) {
    console.error('fetchUrl test failed:', error.message);
    return false;
  }
}

async function testFetchUrls() {
  console.log('Testing fetchUrls...');
  
  try {
    const result = await fetchUrls({
      urls: ['https://httpbin.org/html', 'https://httpbin.org/json'],
      timeout: 10000,
      extractContent: false
    });
    
    console.log('fetchUrls result:', {
      success: result.success,
      hasContent: result.content && result.content.length > 0,
      contentLength: result.content?.[0]?.text?.length || 0,
      error: result.error
    });
    
    return result.success !== false;
  } catch (error) {
    console.error('fetchUrls test failed:', error.message);
    return false;
  }
}

async function testInvalidUrl() {
  console.log('Testing invalid URL handling...');
  
  try {
    const result = await fetchUrl({
      url: 'invalid-url'
    });
    
    console.log('Invalid URL result:', {
      success: result.success,
      isError: result.isError,
      hasError: !!result.error
    });
    
    return result.success === false && result.isError === true;
  } catch (error) {
    console.error('Invalid URL test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('Starting basic functionality tests...\n');
  
  const tests = [
    { name: 'fetchUrl', test: testFetchUrl },
    { name: 'fetchUrls', test: testFetchUrls },
    { name: 'invalid URL', test: testInvalidUrl }
  ];
  
  const results = [];
  
  for (const { name, test } of tests) {
    console.log(`\n--- Testing ${name} ---`);
    try {
      const passed = await test();
      results.push({ name, passed });
      console.log(`✓ ${name}: ${passed ? 'PASSED' : 'FAILED'}`);
    } catch (error) {
      results.push({ name, passed: false });
      console.log(`✗ ${name}: FAILED (${error.message})`);
    }
  }
  
  console.log('\n--- Test Summary ---');
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  
  console.log(`Passed: ${passed}/${total}`);
  
  if (passed === total) {
    console.log('🎉 All tests passed!');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed');
    process.exit(1);
  }
}

// 运行测试
runTests().catch(error => {
  console.error('Test runner failed:', error);
  process.exit(1);
});
